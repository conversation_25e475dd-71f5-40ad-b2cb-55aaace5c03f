<!--
 * @Description: 
 * @Autor: Fhz
 * @Date: 2025-04-24 15:42:28
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-04 15:04:32
-->
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm"> </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const emit = defineEmits(['register', 'reload']);
  const api = useBaseApi('/api/knsLx');
  const { createMessage } = useMessage();

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      {
        field: 'typeName',
        label: '类型名称',
        component: 'Input',
        rules: [{ required: true, message: '请输入类型名称' }],
        ifShow: () => type.value != 3,
      },
      {
        field: 'gradeName',
        label: '等级名称',
        component: 'Input',
        rules: [{ required: true, message: '请输入等级名称' }],
        ifShow: () => type.value == 3,
      },
      {
        field: 'target',
        label: '资助目标',
        component: 'Input',
      },
      {
        field: 'quotaRatio',
        label: '资助名额比例系数',
        component: 'InputNumber',
      },
      {
        field: 'minAnnualIncome',
        label: '年人均收入最小',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入年人均收入最小值' }],
        ifShow: () => type.value == 2,
      },
      {
        field: 'maxAnnualIncome',
        label: '年人均收入最大',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入年人均收入最大值' }],
        ifShow: () => type.value == 2,
      },
      {
        field: 'minSurveyScore',
        label: '问卷得分范围下限',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入问卷得分范围下限' }],
        ifShow: () => type.value == 3,
      },
      {
        field: 'maxSurveyScore',
        label: '问卷得分范围上限',
        component: 'InputNumber',
        rules: [{ required: true, message: '请输入问卷得分范围上限' }],
        ifShow: () => type.value == 3,
      },
      {
        field: 'isActive',
        label: '是否使用',
        component: 'Switch',
        rules: [{ required: true, message: '请选择是否使用' }],
      },
    ],
    labelWidth: 140,
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const id = ref('');

  const getTitle = computed(() => (type.value == 3 ? '困难生等级' : '困难生类型'));
  const type = ref('');

  async function init(data) {
    changeLoading(true);
    resetFields();
    type.value = data.type;
    id.value = data.id;

    try {
      if (id.value) {
        // 获取详情数据
        const { data: detail } = await api.request('get', `/${id.value}`);

        // 数据转换处理
        const formData = {
          typeName: detail.knlxmc, // 类型名称
          gradeName: detail.knlxmc, // 等级名称（同类型名称）
          target: detail.zzmb, // 资助目标
          quotaRatio: Number(detail.zzmeblxs || 0), // 资助名额比例系数
          minAnnualIncome: detail.rjsrzx, // 年人均收入最小
          maxAnnualIncome: detail.rjsrzd, // 年人均收入最大
          minSurveyScore: detail.zxwjdf, // 最小问卷得分
          maxSurveyScore: detail.zdwjdf, // 最大问卷得分
          isActive: Boolean(Number(detail.sfsy || 0)), // 是否使用
        };

        setFieldsValue(formData);
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      createMessage.error('获取详情失败');
    } finally {
      changeLoading(false);
    }
  }

  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    changeOkLoading(true);

    try {
      // 数据转换处理
      const submitData = {
        id: id.value,
        knlxmc: values.typeName || values.gradeName, // 类型名称/等级名称
        zzmb: values.target, // 资助目标
        zzmeblxs: String(values.quotaRatio || ''), // 资助名额比例系数
        rjsrzx: values.minAnnualIncome, // 年人均收入最小
        rjsrzd: values.maxAnnualIncome, // 年人均收入最大
        zxwjdf: values.minSurveyScore, // 最小问卷得分
        zdwjdf: values.maxSurveyScore, // 最大问卷得分
        sfsy: values.isActive ? '1' : '0', // 是否使用
      };

      // 根据是否有ID判断新增还是编辑
      const url = id.value ? `/edit/${id.value}` : '/save';
      const method = id.value ? 'put' : 'post';

      const { msg } = await api.request(method, url, {
        data: submitData,
      });

      createMessage.success(msg || '保存成功');
      closeModal();
      emit('reload');
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      changeOkLoading(false);
    }
  }
</script>
